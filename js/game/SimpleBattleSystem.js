// 王国争霸H5 - 简化战斗系统

class SimpleBattleSystem {
    constructor() {
        this.currentBattle = null;
        this.battleHistory = [];
        this.battleDialog = null;
        
        this.init();
    }

    // 初始化战斗系统
    init() {
        this.createBattleDialog();
        console.log('Simple Battle system initialized');
    }

    // 创建战斗弹窗
    createBattleDialog() {
        // 如果已存在则不重复创建
        if (document.getElementById('battleDialog')) return;

        const dialog = document.createElement('div');
        dialog.id = 'battleDialog';
        dialog.className = 'battle-dialog hidden';
        dialog.innerHTML = `
            <div class="battle-dialog-content">
                <div class="battle-dialog-header">
                    <h3 id="battleDialogTitle">战斗准备</h3>
                    <button id="closeBattleDialog" class="close-btn">&times;</button>
                </div>
                <div class="battle-dialog-body">
                    <div class="battle-armies">
                        <div class="army-section player-army">
                            <h4>我方军队</h4>
                            <div class="army-power">战力: <span id="playerPower">0</span></div>
                            <div id="playerArmyList" class="army-list"></div>
                        </div>
                        <div class="battle-vs">VS</div>
                        <div class="army-section enemy-army">
                            <h4>敌方军队</h4>
                            <div class="army-power">战力: <span id="enemyPower">0</span></div>
                            <div id="enemyArmyList" class="army-list"></div>
                        </div>
                    </div>
                    <div class="battle-progress">
                        <div id="battleProgressBar" class="progress-bar">
                            <div id="battleProgressFill" class="progress-fill"></div>
                        </div>
                        <div id="battleStatus" class="battle-status">准备战斗...</div>
                    </div>
                    <div id="battleRewards" class="battle-rewards">
                        <h4>预期奖励</h4>
                        <div id="rewardsList" class="rewards-list"></div>
                    </div>
                </div>
                <div class="battle-dialog-footer">
                    <button id="startBattleBtn" class="btn btn-primary">开始战斗</button>
                    <button id="cancelBattleBtn" class="btn btn-secondary">取消</button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);
        this.battleDialog = dialog;

        // 绑定事件
        this.setupBattleDialogEvents();
    }

    // 设置战斗弹窗事件
    setupBattleDialogEvents() {
        const closeBtn = document.getElementById('closeBattleDialog');
        const cancelBtn = document.getElementById('cancelBattleBtn');
        const startBtn = document.getElementById('startBattleBtn');

        if (closeBtn) {
            closeBtn.onclick = () => this.hideBattleDialog();
        }
        if (cancelBtn) {
            cancelBtn.onclick = () => this.hideBattleDialog();
        }
        if (startBtn) {
            startBtn.onclick = () => this.executeBattle();
        }

        // 点击遮罩关闭
        this.battleDialog.onclick = (e) => {
            if (e.target === this.battleDialog) {
                this.hideBattleDialog();
            }
        };
    }

    // 开始战斗
    startBattle(stageId, playerUnits = []) {
        const stageData = GameData.battles.campaign.find(stage => stage.id === stageId);
        if (!stageData) {
            console.error('Battle stage not found:', stageId);
            return false;
        }

        // 检查是否有军队（新手训练关卡除外）
        if ((!playerUnits || playerUnits.length === 0) && stageId !== 'stage_0') {
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.WARNING,
                text: '请先选择要出战的军队！'
            });
            return false;
        }
        
        // 新手训练关卡特殊处理
        if (stageId === 'stage_0' && (!playerUnits || playerUnits.length === 0)) {
            const dummyData = GameData.units.dummy;
            playerUnits = [{
                id: Utils.generateId(),
                type: 'dummy',
                name: dummyData.name,
                health: dummyData.health,
                maxHealth: dummyData.health,
                attack: dummyData.attack,
                defense: dummyData.defense,
                speed: dummyData.speed
            }];
        }

        // 创建战斗实例
        this.currentBattle = {
            stageId: stageId,
            stageData: stageData,
            playerUnits: [...playerUnits],
            enemyUnits: this.createEnemyUnits(stageData.enemies),
            playerPower: 0,
            enemyPower: 0,
            winChance: 0
        };

        // 计算战力
        this.calculateBattlePower();

        // 显示战斗弹窗
        this.showBattleDialog();
        
        return true;
    }

    // 创建敌军单位
    createEnemyUnits(enemyData) {
        const enemies = [];
        
        for (const enemy of enemyData) {
            const unitData = GameData.units[enemy.type];
            if (!unitData) continue;
            
            for (let i = 0; i < enemy.count; i++) {
                enemies.push({
                    id: Utils.generateId(),
                    type: enemy.type,
                    name: unitData.name,
                    health: unitData.health,
                    maxHealth: unitData.health,
                    attack: unitData.attack,
                    defense: unitData.defense,
                    speed: unitData.speed,
                    count: 1
                });
            }
        }
        
        return enemies;
    }

    // 计算战斗力
    calculateBattlePower() {
        const battle = this.currentBattle;
        
        // 计算玩家战力
        battle.playerPower = this.calculateArmyPower(battle.playerUnits);
        
        // 计算敌军战力
        battle.enemyPower = this.calculateArmyPower(battle.enemyUnits);
        
        // 计算胜率
        const totalPower = battle.playerPower + battle.enemyPower;
        battle.winChance = totalPower > 0 ? battle.playerPower / totalPower : 0.5;
        
        console.log(`战力对比: 玩家${battle.playerPower} vs 敌军${battle.enemyPower}, 胜率${(battle.winChance * 100).toFixed(1)}%`);
    }

    // 计算军队战力
    calculateArmyPower(units) {
        let totalPower = 0;
        
        for (const unit of units) {
            // 单位战力 = (攻击力 + 防御力 + 生命值/10 + 速度/5) * 数量
            const unitPower = (
                (unit.attack || 0) + 
                (unit.defense || 0) + 
                (unit.health || 0) / 10 + 
                (unit.speed || 0) / 5
            ) * (unit.count || 1);
            
            totalPower += unitPower;
        }
        
        return Math.round(totalPower);
    }

    // 显示战斗弹窗
    showBattleDialog() {
        const battle = this.currentBattle;
        if (!battle) return;

        // 更新标题
        const title = document.getElementById('battleDialogTitle');
        if (title) {
            title.textContent = `战斗：${battle.stageData.name}`;
        }

        // 更新战力显示
        const playerPowerEl = document.getElementById('playerPower');
        const enemyPowerEl = document.getElementById('enemyPower');
        if (playerPowerEl) playerPowerEl.textContent = battle.playerPower;
        if (enemyPowerEl) enemyPowerEl.textContent = battle.enemyPower;

        // 显示胜率预测
        this.updateWinChanceDisplay();

        // 更新军队列表
        this.updateArmyDisplay('playerArmyList', battle.playerUnits);
        this.updateArmyDisplay('enemyArmyList', battle.enemyUnits);

        // 更新奖励显示
        this.updateRewardsDisplay();

        // 重置进度条
        this.resetBattleProgress();

        // 显示弹窗
        this.battleDialog.classList.remove('hidden');
    }

    // 隐藏战斗弹窗
    hideBattleDialog() {
        if (this.battleDialog) {
            this.battleDialog.classList.add('hidden');
        }
        // 清理胜率显示
        const winChanceDisplay = document.getElementById('winChanceDisplay');
        if (winChanceDisplay) {
            winChanceDisplay.remove();
        }
        // 清理战斗日志
        const logContainer = document.getElementById('battleLogContainer');
        if (logContainer) {
            logContainer.remove();
        }
        // 注意：不要在这里设置 currentBattle = null，这会导致无法再次战斗
    }

    // 重置系统
    reset() {
        this.currentBattle = null;
        this.battleHistory = [];
        this.hideBattleDialog();
    }

    // 更新军队显示
    updateArmyDisplay(containerId, units) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = '';

        // 统计相同类型的单位
        const unitCounts = {};
        for (const unit of units) {
            if (unitCounts[unit.type]) {
                unitCounts[unit.type].count++;
            } else {
                unitCounts[unit.type] = {
                    name: unit.name,
                    count: 1,
                    attack: unit.attack,
                    defense: unit.defense,
                    health: unit.health
                };
            }
        }

        // 显示单位
        for (const [type, data] of Object.entries(unitCounts)) {
            const unitEl = document.createElement('div');
            unitEl.className = 'army-unit';
            unitEl.innerHTML = `
                <div class="unit-info">
                    <span class="unit-name">${data.name} x${data.count}</span>
                    <span class="unit-stats">⚔️${data.attack} 🛡️${data.defense} ❤️${data.health}</span>
                </div>
            `;
            container.appendChild(unitEl);
        }
    }

    // 更新胜率显示
    updateWinChanceDisplay() {
        const battle = this.currentBattle;
        if (!battle) return;

        // 在战力显示区域添加胜率信息
        const playerSection = document.querySelector('.player-army');
        const enemySection = document.querySelector('.enemy-army');

        if (playerSection && enemySection) {
            // 移除旧的胜率显示
            const oldWinChance = document.getElementById('winChanceDisplay');
            if (oldWinChance) oldWinChance.remove();

            // 创建胜率显示
            const winChanceEl = document.createElement('div');
            winChanceEl.id = 'winChanceDisplay';
            winChanceEl.className = 'win-chance-display';

            const winPercentage = Math.round(battle.winChance * 100);
            let winChanceColor = '#e74c3c'; // 红色 - 劣势
            let winChanceText = '劣势';

            if (winPercentage >= 70) {
                winChanceColor = '#27ae60'; // 绿色 - 优势
                winChanceText = '优势';
            } else if (winPercentage >= 50) {
                winChanceColor = '#f39c12'; // 橙色 - 均势
                winChanceText = '均势';
            }

            winChanceEl.innerHTML = `
                <div class="win-chance-header">战力分析</div>
                <div class="win-chance-bar">
                    <div class="win-chance-fill" style="width: ${winPercentage}%; background-color: ${winChanceColor}"></div>
                </div>
                <div class="win-chance-text" style="color: ${winChanceColor}">
                    预计胜率: ${winPercentage}% (${winChanceText})
                </div>
                <div class="battle-tips">
                    ${this.getBattleTips(winPercentage)}
                </div>
            `;

            // 插入到VS区域
            const vsSection = document.querySelector('.battle-vs');
            if (vsSection) {
                vsSection.parentNode.insertBefore(winChanceEl, vsSection.nextSibling);
            }
        }
    }

    // 获取战斗建议
    getBattleTips(winPercentage) {
        if (winPercentage >= 80) {
            return '💪 实力碾压，轻松获胜！';
        } else if (winPercentage >= 60) {
            return '✅ 胜算较大，可以一战！';
        } else if (winPercentage >= 40) {
            return '⚠️ 势均力敌，需要运气！';
        } else {
            return '🚨 实力不足，建议提升后再战！';
        }
    }

    // 更新奖励显示
    updateRewardsDisplay() {
        const battle = this.currentBattle;
        const container = document.getElementById('rewardsList');
        if (!container || !battle) return;

        const rewards = battle.stageData.rewards;
        container.innerHTML = '';

        for (const [resource, amount] of Object.entries(rewards)) {
            const rewardEl = document.createElement('div');
            rewardEl.className = 'reward-item';

            let icon = '';
            switch (resource) {
                case 'gold': icon = '💰'; break;
                case 'exp': icon = '⭐'; break;
                case 'food': icon = '🍖'; break;
                case 'wood': icon = '🪵'; break;
                case 'stone': icon = '🪨'; break;
                default: icon = '🎁'; break;
            }

            rewardEl.innerHTML = `${icon} ${amount}`;
            container.appendChild(rewardEl);
        }
    }

    // 重置战斗进度
    resetBattleProgress() {
        const progressFill = document.getElementById('battleProgressFill');
        const status = document.getElementById('battleStatus');

        if (progressFill) {
            progressFill.style.width = '0%';
        }
        if (status) {
            status.textContent = '准备战斗...';
        }

        // 清理战斗日志
        const logContainer = document.getElementById('battleLogContainer');
        if (logContainer) {
            logContainer.remove();
        }

        // 重置按钮状态
        const startBtn = document.getElementById('startBattleBtn');
        const cancelBtn = document.getElementById('cancelBattleBtn');
        if (startBtn) {
            startBtn.disabled = false;
            startBtn.textContent = '开始战斗';
        }
        if (cancelBtn) {
            cancelBtn.disabled = false;
        }
    }

    // 执行战斗
    executeBattle() {
        const battle = this.currentBattle;
        if (!battle) return;

        // 禁用按钮
        const startBtn = document.getElementById('startBattleBtn');
        const cancelBtn = document.getElementById('cancelBattleBtn');
        if (startBtn) startBtn.disabled = true;
        if (cancelBtn) cancelBtn.disabled = true;

        // 开始战斗动画
        this.animateBattle();
    }

    // 战斗动画
    animateBattle() {
        const progressFill = document.getElementById('battleProgressFill');
        const status = document.getElementById('battleStatus');

        if (status) status.textContent = '战斗开始...';

        // 创建战斗日志容器
        this.createBattleLog();

        // 模拟战斗过程
        this.simulateBattleProcess();
    }

    // 创建战斗日志容器
    createBattleLog() {
        // 检查是否已存在战斗日志
        let logContainer = document.getElementById('battleLogContainer');
        if (!logContainer) {
            logContainer = document.createElement('div');
            logContainer.id = 'battleLogContainer';
            logContainer.className = 'battle-log-container';
            logContainer.innerHTML = `
                <div class="battle-log-header">⚔️ 战斗过程</div>
                <div id="battleLogContent" class="battle-log-content"></div>
            `;

            // 插入到进度条下方
            const progressSection = document.querySelector('.battle-progress');
            if (progressSection) {
                progressSection.parentNode.insertBefore(logContainer, progressSection.nextSibling);
            }
        }
    }

    // 模拟战斗过程
    simulateBattleProcess() {
        const battle = this.currentBattle;
        const progressFill = document.getElementById('battleProgressFill');
        const status = document.getElementById('battleStatus');

        let progress = 0;
        let battleRound = 1;
        const maxRounds = 5; // 模拟5回合战斗

        const battleInterval = setInterval(() => {
            progress += 20; // 每回合20%进度

            if (progressFill) {
                progressFill.style.width = progress + '%';
            }

            // 模拟当前回合的战斗
            if (battleRound <= maxRounds) {
                this.simulateRound(battleRound);

                if (status) {
                    status.textContent = `第${battleRound}回合战斗中...`;
                }

                battleRound++;
            }

            if (progress >= 100) {
                clearInterval(battleInterval);

                if (status) {
                    status.textContent = '战斗结束，计算结果...';
                }

                // 计算战斗结果
                setTimeout(() => {
                    this.calculateBattleResult();
                }, 800);
            }
        }, 1000); // 每秒一回合
    }

    // 模拟单回合战斗
    simulateRound(round) {
        const battle = this.currentBattle;
        const logContent = document.getElementById('battleLogContent');
        if (!logContent) return;

        // 添加回合开始信息
        this.addBattleLogMessage(`🔥 第${round}回合开始！`, 'round-start');

        // 模拟我方攻击
        setTimeout(() => {
            const playerUnit = battle.playerUnits[Math.floor(Math.random() * battle.playerUnits.length)];
            const enemyUnit = battle.enemyUnits[Math.floor(Math.random() * battle.enemyUnits.length)];

            const damage = Math.floor(Math.random() * 20) + 10;
            this.addBattleLogMessage(
                `⚔️ ${playerUnit.name} 攻击 ${enemyUnit.name}，造成 ${damage} 点伤害！`,
                'player-attack'
            );
        }, 200);

        // 模拟敌方攻击
        setTimeout(() => {
            const enemyUnit = battle.enemyUnits[Math.floor(Math.random() * battle.enemyUnits.length)];
            const playerUnit = battle.playerUnits[Math.floor(Math.random() * battle.playerUnits.length)];

            const damage = Math.floor(Math.random() * 15) + 8;
            this.addBattleLogMessage(
                `🗡️ ${enemyUnit.name} 攻击 ${playerUnit.name}，造成 ${damage} 点伤害！`,
                'enemy-attack'
            );
        }, 500);

        // 随机事件
        if (Math.random() < 0.3) {
            setTimeout(() => {
                const events = [
                    '💥 暴击！造成额外伤害！',
                    '🛡️ 成功格挡，减少伤害！',
                    '⚡ 连击！发动连续攻击！',
                    '🎯 精准打击！命中要害！'
                ];
                const event = events[Math.floor(Math.random() * events.length)];
                this.addBattleLogMessage(event, 'special-event');
            }, 700);
        }
    }

    // 添加战斗日志消息
    addBattleLogMessage(message, type) {
        const logContent = document.getElementById('battleLogContent');
        if (!logContent) return;

        const messageEl = document.createElement('div');
        messageEl.className = `battle-log-message ${type}`;
        messageEl.textContent = message;

        logContent.appendChild(messageEl);

        // 自动滚动到底部
        logContent.scrollTop = logContent.scrollHeight;

        // 添加淡入动画
        messageEl.style.opacity = '0';
        messageEl.style.transform = 'translateY(10px)';

        setTimeout(() => {
            messageEl.style.transition = 'all 0.3s ease';
            messageEl.style.opacity = '1';
            messageEl.style.transform = 'translateY(0)';
        }, 50);
    }

    // 计算战斗结果
    calculateBattleResult() {
        const battle = this.currentBattle;

        // 基础胜率
        let winChance = battle.winChance;

        // 添加随机因子 (80%-120%)
        const randomFactor = 0.8 + Math.random() * 0.4;
        winChance *= randomFactor;

        // 判定胜负
        const victory = winChance > 0.5;

        // 计算伤亡
        const result = this.calculateCasualties(victory, winChance);

        // 显示结果
        this.showBattleResult(result);
    }

    // 计算伤亡
    calculateCasualties(victory, winChance) {
        const battle = this.currentBattle;
        let playerLosses = [];
        let enemyLosses = [];

        if (victory) {
            // 玩家胜利
            const lossRate = Math.max(0, (1 - winChance) * 0.3); // 最多30%损失
            playerLosses = this.applyLosses(battle.playerUnits, lossRate);
            enemyLosses = [...battle.enemyUnits]; // 敌军全灭
        } else {
            // 玩家失败
            const lossRate = 0.5 + Math.random() * 0.3; // 50-80%损失
            playerLosses = this.applyLosses(battle.playerUnits, lossRate);
            const enemyLossRate = winChance * 0.4; // 部分损失
            enemyLosses = this.applyLosses(battle.enemyUnits, enemyLossRate);
        }

        return {
            victory: victory,
            playerLosses: playerLosses,
            enemyLosses: enemyLosses,
            winChance: winChance
        };
    }

    // 应用损失
    applyLosses(units, lossRate) {
        const losses = [];
        const unitsToLose = Math.floor(units.length * lossRate);

        for (let i = 0; i < unitsToLose; i++) {
            if (units[i]) {
                losses.push(units[i]);
            }
        }

        return losses;
    }

    // 显示战斗结果
    showBattleResult(result) {
        const status = document.getElementById('battleStatus');
        const footer = document.querySelector('.battle-dialog-footer');
        const battle = this.currentBattle;

        // 添加最终战斗结果到日志
        if (result.victory) {
            this.addBattleLogMessage('🎉 战斗胜利！敌军全军覆没！', 'victory');
            if (status) {
                status.innerHTML = '🏆 <span style="color: #f39c12;">胜利！</span>';
            }
        } else {
            this.addBattleLogMessage('💀 战斗失败！我军败退...', 'defeat');
            if (status) {
                status.innerHTML = '💀 <span style="color: #e74c3c;">失败！</span>';
            }
        }

        // 显示详细的战斗结果
        if (footer) {
            const rewards = battle.stageData.rewards;
            const rewardText = result.victory ? this.formatRewards(rewards) : '无奖励';

            footer.innerHTML = `
                <div class="battle-result-detailed">
                    <div class="result-header">
                        <h4>${result.victory ? '🏆 战斗胜利' : '💀 战斗失败'}</h4>
                    </div>
                    <div class="result-stats">
                        <div class="stat-row">
                            <span class="stat-label">战斗评价:</span>
                            <span class="stat-value ${result.victory ? 'victory' : 'defeat'}">
                                ${this.getBattleRating(result)}
                            </span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">我方损失:</span>
                            <span class="stat-value">${result.playerLosses.length} 个单位</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">敌方损失:</span>
                            <span class="stat-value">${result.enemyLosses.length} 个单位</span>
                        </div>
                        <div class="stat-row">
                            <span class="stat-label">获得奖励:</span>
                            <span class="stat-value rewards">${rewardText}</span>
                        </div>
                    </div>
                </div>
                <button id="confirmBattleResult" class="btn btn-primary">
                    ${result.victory ? '领取奖励' : '确定'}
                </button>
            `;

            const confirmBtn = document.getElementById('confirmBattleResult');
            if (confirmBtn) {
                confirmBtn.onclick = () => {
                    this.finalizeBattle(result);
                };
            }
        }
    }

    // 获取战斗评价
    getBattleRating(result) {
        if (!result.victory) {
            return '惨败';
        }

        const lossRate = result.playerLosses.length / this.currentBattle.playerUnits.length;

        if (lossRate === 0) {
            return '完美胜利';
        } else if (lossRate <= 0.1) {
            return '大胜';
        } else if (lossRate <= 0.3) {
            return '胜利';
        } else {
            return '惨胜';
        }
    }

    // 格式化奖励显示
    formatRewards(rewards) {
        if (!rewards) return '无';

        return Object.entries(rewards)
            .map(([resource, amount]) => {
                let icon = '';
                switch (resource) {
                    case 'gold': icon = '💰'; break;
                    case 'exp': icon = '⭐'; break;
                    case 'food': icon = '🍖'; break;
                    case 'wood': icon = '🪵'; break;
                    case 'stone': icon = '🪨'; break;
                    default: icon = '🎁'; break;
                }
                return `${icon}${amount}`;
            })
            .join(' ');
    }

    // 完成战斗
    finalizeBattle(result) {
        const battle = this.currentBattle;

        if (result.victory) {
            // 应用奖励
            this.applyBattleRewards(battle.stageData.rewards);

            // 发送胜利事件
            GameEvents.emit(GAME_EVENTS.BATTLE_WIN, {
                stageId: battle.stageId,
                rewards: battle.stageData.rewards
            });
        } else {
            // 发送失败事件
            GameEvents.emit(GAME_EVENTS.BATTLE_LOSE, {
                stageId: battle.stageId
            });
        }

        // 更新军队状态
        if (window.Game.army) {
            window.Game.army.returnArmy(battle.playerUnits, result.playerLosses);
        }

        // 记录战斗历史
        this.battleHistory.push({
            stageId: battle.stageId,
            victory: result.victory,
            timestamp: Date.now(),
            playerLosses: result.playerLosses.length,
            enemyLosses: result.enemyLosses.length
        });

        // 关闭弹窗
        this.hideBattleDialog();

        // 清理当前战斗状态，允许重新开始战斗
        this.currentBattle = null;

        // 发送战斗结束事件
        GameEvents.emit(GAME_EVENTS.BATTLE_END, {
            battle: battle,
            result: result
        });
    }

    // 应用战斗奖励
    applyBattleRewards(rewards) {
        for (const [resource, amount] of Object.entries(rewards)) {
            if (resource === 'exp') {
                // 经验奖励
                if (window.Game.gainExp) {
                    window.Game.gainExp(amount);
                }
            } else {
                // 资源奖励
                if (window.Game.resources) {
                    window.Game.resources.addResource(resource, amount);
                }
            }
        }

        // 显示奖励消息
        const rewardText = Object.entries(rewards)
            .map(([resource, amount]) => {
                let icon = '';
                switch (resource) {
                    case 'gold': icon = '💰'; break;
                    case 'exp': icon = '⭐'; break;
                    case 'food': icon = '🍖'; break;
                    case 'wood': icon = '🪵'; break;
                    case 'stone': icon = '🪨'; break;
                    default: icon = '🎁'; break;
                }
                return `${icon}+${amount}`;
            })
            .join(' ');

        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.SUCCESS,
            text: `战斗胜利！获得奖励：${rewardText}`
        });
    }

    // 获取战斗历史
    getBattleHistory() {
        return this.battleHistory;
    }
}

// 导出战斗系统
window.SimpleBattleSystem = SimpleBattleSystem;
