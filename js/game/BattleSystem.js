// 王国争霸H5 - 简化战斗系统

class SimpleBattleSystem {
    constructor() {
        this.currentBattle = null;
        this.battleHistory = [];
        this.battleDialog = null;

        this.init();
    }

    // 初始化战斗系统
    init() {
        this.createBattleDialog();
        console.log('Simple Battle system initialized');
    }

    // 创建战斗弹窗
    createBattleDialog() {
        // 如果已存在则不重复创建
        if (document.getElementById('battleDialog')) return;

        const dialog = document.createElement('div');
        dialog.id = 'battleDialog';
        dialog.className = 'battle-dialog hidden';
        dialog.innerHTML = `
            <div class="battle-dialog-content">
                <div class="battle-dialog-header">
                    <h3 id="battleDialogTitle">战斗准备</h3>
                    <button id="closeBattleDialog" class="close-btn">&times;</button>
                </div>
                <div class="battle-dialog-body">
                    <div class="battle-armies">
                        <div class="army-section player-army">
                            <h4>我方军队</h4>
                            <div class="army-power">战力: <span id="playerPower">0</span></div>
                            <div id="playerArmyList" class="army-list"></div>
                        </div>
                        <div class="battle-vs">VS</div>
                        <div class="army-section enemy-army">
                            <h4>敌方军队</h4>
                            <div class="army-power">战力: <span id="enemyPower">0</span></div>
                            <div id="enemyArmyList" class="army-list"></div>
                        </div>
                    </div>
                    <div class="battle-progress">
                        <div id="battleProgressBar" class="progress-bar">
                            <div id="battleProgressFill" class="progress-fill"></div>
                        </div>
                        <div id="battleStatus" class="battle-status">准备战斗...</div>
                    </div>
                    <div id="battleRewards" class="battle-rewards">
                        <h4>预期奖励</h4>
                        <div id="rewardsList" class="rewards-list"></div>
                    </div>
                </div>
                <div class="battle-dialog-footer">
                    <button id="startBattleBtn" class="btn btn-primary">开始战斗</button>
                    <button id="cancelBattleBtn" class="btn btn-secondary">取消</button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);
        this.battleDialog = dialog;

        // 绑定事件
        this.setupBattleDialogEvents();
    }

    // 设置战斗弹窗事件
    setupBattleDialogEvents() {
        const closeBtn = document.getElementById('closeBattleDialog');
        const cancelBtn = document.getElementById('cancelBattleBtn');
        const startBtn = document.getElementById('startBattleBtn');

        if (closeBtn) {
            closeBtn.onclick = () => this.hideBattleDialog();
        }
        if (cancelBtn) {
            cancelBtn.onclick = () => this.hideBattleDialog();
        }
        if (startBtn) {
            startBtn.onclick = () => this.executeBattle();
        }

        // 点击遮罩关闭
        this.battleDialog.onclick = (e) => {
            if (e.target === this.battleDialog) {
                this.hideBattleDialog();
            }
        };
    }

    // 开始战斗
    startBattle(stageId, playerUnits = []) {
        const stageData = GameData.battles.campaign.find(stage => stage.id === stageId);
        if (!stageData) {
            console.error('Battle stage not found:', stageId);
            return false;
        }

        // 检查是否有军队（新手训练关卡除外）
        if ((!playerUnits || playerUnits.length === 0) && stageId !== 'stage_0') {
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.WARNING,
                text: '请先选择要出战的军队！'
            });
            return false;
        }

        // 新手训练关卡特殊处理
        if (stageId === 'stage_0' && (!playerUnits || playerUnits.length === 0)) {
            const dummyData = GameData.units.dummy;
            playerUnits = [{
                id: Utils.generateId(),
                type: 'dummy',
                name: dummyData.name,
                health: dummyData.health,
                maxHealth: dummyData.health,
                attack: dummyData.attack,
                defense: dummyData.defense,
                speed: dummyData.speed
            }];
        }

        // 创建战斗实例
        this.currentBattle = {
            stageId: stageId,
            stageData: stageData,
            playerUnits: [...playerUnits],
            enemyUnits: this.createEnemyUnits(stageData.enemies),
            playerPower: 0,
            enemyPower: 0,
            winChance: 0
        };

        // 计算战力
        this.calculateBattlePower();

        // 显示战斗弹窗
        this.showBattleDialog();

        return true;
    }

    // 创建敌军单位
    createEnemyUnits(enemyData) {
        const enemies = [];

        for (const enemy of enemyData) {
            const unitData = GameData.units[enemy.type];
            if (!unitData) continue;

            for (let i = 0; i < enemy.count; i++) {
                enemies.push({
                    id: Utils.generateId(),
                    type: enemy.type,
                    name: unitData.name,
                    health: unitData.health,
                    maxHealth: unitData.health,
                    attack: unitData.attack,
                    defense: unitData.defense,
                    speed: unitData.speed,
                    count: 1
                });
            }
        }

        return enemies;
    }

    // 计算战斗力
    calculateBattlePower() {
        const battle = this.currentBattle;

        // 计算玩家战力
        battle.playerPower = this.calculateArmyPower(battle.playerUnits);

        // 计算敌军战力
        battle.enemyPower = this.calculateArmyPower(battle.enemyUnits);

        // 计算胜率
        const totalPower = battle.playerPower + battle.enemyPower;
        battle.winChance = totalPower > 0 ? battle.playerPower / totalPower : 0.5;

        console.log(`战力对比: 玩家${battle.playerPower} vs 敌军${battle.enemyPower}, 胜率${(battle.winChance * 100).toFixed(1)}%`);
    }

    // 计算军队战力
    calculateArmyPower(units) {
        let totalPower = 0;

        for (const unit of units) {
            // 单位战力 = (攻击力 + 防御力 + 生命值/10 + 速度/5) * 数量
            const unitPower = (
                (unit.attack || 0) +
                (unit.defense || 0) +
                (unit.health || 0) / 10 +
                (unit.speed || 0) / 5
            ) * (unit.count || 1);

            totalPower += unitPower;
        }

        return Math.round(totalPower);
    }

    // 显示战斗弹窗
    showBattleDialog() {
        const battle = this.currentBattle;
        if (!battle) return;

        // 更新标题
        const title = document.getElementById('battleDialogTitle');
        if (title) {
            title.textContent = `战斗：${battle.stageData.name}`;
        }

        // 更新战力显示
        const playerPowerEl = document.getElementById('playerPower');
        const enemyPowerEl = document.getElementById('enemyPower');
        if (playerPowerEl) playerPowerEl.textContent = battle.playerPower;
        if (enemyPowerEl) enemyPowerEl.textContent = battle.enemyPower;

        // 更新军队列表
        this.updateArmyDisplay('playerArmyList', battle.playerUnits);
        this.updateArmyDisplay('enemyArmyList', battle.enemyUnits);

        // 更新奖励显示
        this.updateRewardsDisplay();

        // 重置进度条
        this.resetBattleProgress();

        // 显示弹窗
        this.battleDialog.classList.remove('hidden');
    }

    // 隐藏战斗弹窗
    hideBattleDialog() {
        if (this.battleDialog) {
            this.battleDialog.classList.add('hidden');
        }
        this.currentBattle = null;
    }

    // 更新军队显示
    updateArmyDisplay(containerId, units) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = '';

        // 统计相同类型的单位
        const unitCounts = {};
        for (const unit of units) {
            if (unitCounts[unit.type]) {
                unitCounts[unit.type].count++;
            } else {
                unitCounts[unit.type] = {
                    name: unit.name,
                    count: 1,
                    attack: unit.attack,
                    defense: unit.defense,
                    health: unit.health
                };
            }
        }

        // 显示单位
        for (const [type, data] of Object.entries(unitCounts)) {
            const unitEl = document.createElement('div');
            unitEl.className = 'army-unit';
            unitEl.innerHTML = `
                <div class="unit-info">
                    <span class="unit-name">${data.name} x${data.count}</span>
                    <span class="unit-stats">⚔️${data.attack} 🛡️${data.defense} ❤️${data.health}</span>
                </div>
            `;
            container.appendChild(unitEl);
        }
    }

    // 更新奖励显示
    updateRewardsDisplay() {
        const battle = this.currentBattle;
        const container = document.getElementById('rewardsList');
        if (!container || !battle) return;

        const rewards = battle.stageData.rewards;
        container.innerHTML = '';

        for (const [resource, amount] of Object.entries(rewards)) {
            const rewardEl = document.createElement('div');
            rewardEl.className = 'reward-item';

            let icon = '';
            switch (resource) {
                case 'gold': icon = '💰'; break;
                case 'exp': icon = '⭐'; break;
                case 'food': icon = '🍖'; break;
                case 'wood': icon = '🪵'; break;
                case 'stone': icon = '🪨'; break;
                default: icon = '🎁'; break;
            }

            rewardEl.innerHTML = `${icon} ${amount}`;
            container.appendChild(rewardEl);
        }
    }

    // 重置战斗进度
    resetBattleProgress() {
        const progressFill = document.getElementById('battleProgressFill');
        const status = document.getElementById('battleStatus');

        if (progressFill) {
            progressFill.style.width = '0%';
        }
        if (status) {
            status.textContent = '准备战斗...';
        }
    }

    // 执行战斗
    executeBattle() {
        const battle = this.currentBattle;
        if (!battle) return;

        // 禁用按钮
        const startBtn = document.getElementById('startBattleBtn');
        const cancelBtn = document.getElementById('cancelBattleBtn');
        if (startBtn) startBtn.disabled = true;
        if (cancelBtn) cancelBtn.disabled = true;

        // 开始战斗动画
        this.animateBattle();
    }

    // 战斗动画
    animateBattle() {
        const progressFill = document.getElementById('battleProgressFill');
        const status = document.getElementById('battleStatus');

        if (status) status.textContent = '战斗进行中...';

        // 进度条动画
        let progress = 0;
        const interval = setInterval(() => {
            progress += 2;
            if (progressFill) {
                progressFill.style.width = progress + '%';
            }

            if (progress >= 100) {
                clearInterval(interval);
                // 计算战斗结果
                setTimeout(() => {
                    this.calculateBattleResult();
                }, 500);
            }
        }, 50); // 2.5秒完成进度条
    }

    // 计算战斗结果
    calculateBattleResult() {
        const battle = this.currentBattle;

        // 基础胜率
        let winChance = battle.winChance;

        // 添加随机因子 (80%-120%)
        const randomFactor = 0.8 + Math.random() * 0.4;
        winChance *= randomFactor;

        // 判定胜负
        const victory = winChance > 0.5;

        // 计算伤亡
        const result = this.calculateCasualties(victory, winChance);

        // 显示结果
        this.showBattleResult(result);
    }

    // 计算伤亡
    calculateCasualties(victory, winChance) {
        const battle = this.currentBattle;
        let playerLosses = [];
        let enemyLosses = [];

        if (victory) {
            // 玩家胜利
            const lossRate = Math.max(0, (1 - winChance) * 0.3); // 最多30%损失
            playerLosses = this.applyLosses(battle.playerUnits, lossRate);
            enemyLosses = [...battle.enemyUnits]; // 敌军全灭
        } else {
            // 玩家失败
            const lossRate = 0.5 + Math.random() * 0.3; // 50-80%损失
            playerLosses = this.applyLosses(battle.playerUnits, lossRate);
            const enemyLossRate = winChance * 0.4; // 部分损失
            enemyLosses = this.applyLosses(battle.enemyUnits, enemyLossRate);
        }

        return {
            victory: victory,
            playerLosses: playerLosses,
            enemyLosses: enemyLosses,
            winChance: winChance
        };
    }

    // 应用损失
    applyLosses(units, lossRate) {
        const losses = [];
        const unitsToLose = Math.floor(units.length * lossRate);

        for (let i = 0; i < unitsToLose; i++) {
            if (units[i]) {
                losses.push(units[i]);
            }
        }

        return losses;
    }

    // 显示战斗结果
    showBattleResult(result) {
        const status = document.getElementById('battleStatus');
        const footer = document.querySelector('.battle-dialog-footer');

        if (status) {
            if (result.victory) {
                status.innerHTML = '🏆 <span style="color: #f39c12;">胜利！</span>';
            } else {
                status.innerHTML = '💀 <span style="color: #e74c3c;">失败！</span>';
            }
        }

        // 更新按钮
        if (footer) {
            footer.innerHTML = `
                <div class="battle-result-info">
                    <div class="result-stats">
                        <div>我方损失: ${result.playerLosses.length}个单位</div>
                        <div>敌方损失: ${result.enemyLosses.length}个单位</div>
                    </div>
                </div>
                <button id="confirmBattleResult" class="btn btn-primary">确定</button>
            `;

            const confirmBtn = document.getElementById('confirmBattleResult');
            if (confirmBtn) {
                confirmBtn.onclick = () => {
                    this.finalizeBattle(result);
                };
            }
        }
    }

    // 完成战斗
    finalizeBattle(result) {
        const battle = this.currentBattle;

        if (result.victory) {
            // 应用奖励
            this.applyBattleRewards(battle.stageData.rewards);

            // 发送胜利事件
            GameEvents.emit(GAME_EVENTS.BATTLE_WIN, {
                stageId: battle.stageId,
                rewards: battle.stageData.rewards
            });
        } else {
            // 发送失败事件
            GameEvents.emit(GAME_EVENTS.BATTLE_LOSE, {
                stageId: battle.stageId
            });
        }

        // 更新军队状态
        if (window.Game.army) {
            window.Game.army.returnArmy(battle.playerUnits, result.playerLosses);
        }

        // 记录战斗历史
        this.battleHistory.push({
            stageId: battle.stageId,
            victory: result.victory,
            timestamp: Date.now(),
            playerLosses: result.playerLosses.length,
            enemyLosses: result.enemyLosses.length
        });

        // 关闭弹窗
        this.hideBattleDialog();

        // 发送战斗结束事件
        GameEvents.emit(GAME_EVENTS.BATTLE_END, {
            battle: battle,
            result: result
        });
    }

    // 应用战斗奖励
    applyBattleRewards(rewards) {
        for (const [resource, amount] of Object.entries(rewards)) {
            if (resource === 'exp') {
                // 经验奖励
                if (window.Game.gainExp) {
                    window.Game.gainExp(amount);
                }
            } else {
                // 资源奖励
                if (window.Game.resources) {
                    window.Game.resources.addResource(resource, amount);
                }
            }
        }

        // 显示奖励消息
        const rewardText = Object.entries(rewards)
            .map(([resource, amount]) => {
                let icon = '';
                switch (resource) {
                    case 'gold': icon = '💰'; break;
                    case 'exp': icon = '⭐'; break;
                    case 'food': icon = '🍖'; break;
                    case 'wood': icon = '🪵'; break;
                    case 'stone': icon = '🪨'; break;
                    default: icon = '🎁'; break;
                }
                return `${icon}+${amount}`;
            })
            .join(' ');

        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.SUCCESS,
            text: `战斗胜利！获得奖励：${rewardText}`
        });
    }

    // 重置系统
    reset() {
        this.currentBattle = null;
        this.battleHistory = [];
        this.hideBattleDialog();
    }

    // 获取战斗历史
    getBattleHistory() {
        return this.battleHistory;
    }
}

// 导出战斗系统
window.SimpleBattleSystem = SimpleBattleSystem;
            ...this.currentBattle.playerUnits.map(unit => ({...unit, side: 'player'})),
            ...this.currentBattle.enemyUnits.map(unit => ({...unit, side: 'enemy'}))
        ];

        // 按速度排序，速度高的先行动
        return allUnits
            .filter(unit => unit.health > 0)
            .sort((a, b) => {
                const speedA = a.getSpeed ? a.getSpeed() : a.speed;
                const speedB = b.getSpeed ? b.getSpeed() : b.speed;
                return speedB - speedA;
            });
    }

    // 处理单个回合
    processTurn() {
        const battle = this.currentBattle;

        if (!battle.turnOrder || battle.turnOrder.length === 0) {
            this.checkBattleEnd();
            return;
        }

        // 获取当前行动的单位
        const currentUnit = battle.turnOrder[battle.currentTurnIndex];

        if (!currentUnit || currentUnit.health <= 0) {
            this.nextTurn();
            return;
        }

        // 执行行动
        setTimeout(() => {
            this.executeUnitAction(currentUnit);
        }, 800); // 每个行动间隔0.8秒
    }

    // 执行单位行动
    executeUnitAction(unit) {
        const battle = this.currentBattle;

        // 选择目标
        const targets = this.selectTargets(unit);

        if (targets.length === 0) {
            this.nextTurn();
            return;
        }

        // 执行攻击
        const target = targets[0]; // 简化：选择第一个目标
        const damage = this.calculateDamage(unit, target);

        // 应用伤害
        target.health = Math.max(0, target.health - damage);

        // 记录战斗日志
        const attackerName = `${unit.side === 'player' ? '我方' : '敌方'}${unit.name}`;
        const targetName = `${target.side === 'player' ? '我方' : '敌方'}${target.name}`;

        battle.battleLog.push({
            round: battle.round,
            action: 'attack',
            attacker: attackerName,
            target: targetName,
            damage: damage,
            targetHealth: target.health
        });

        // 更新UI
        if (window.Game.ui) {
            window.Game.ui.addBattleLog(`${attackerName}攻击${targetName}，造成${damage}点伤害！`, 'damage');
            window.Game.ui.updateBattleUnits();
        }

        // 检查目标是否死亡
        if (target.health <= 0) {
            if (window.Game.ui) {
                window.Game.ui.addBattleLog(`${targetName}被击败！`, 'info');
            }
        }

        // 下一个回合
        setTimeout(() => {
            this.nextTurn();
        }, this.animationDelay);
    }

    // 下一个回合
    nextTurn() {
        const battle = this.currentBattle;

        battle.currentTurnIndex++;

        // 如果所有单位都行动完毕，开始新的回合
        if (battle.currentTurnIndex >= battle.turnOrder.length) {
            battle.round++;
            battle.currentTurnIndex = 0;

            // 重新计算行动顺序（移除死亡单位）
            battle.turnOrder = this.calculateTurnOrder();

            if (battle.turnOrder.length === 0) {
                this.checkBattleEnd();
                return;
            }

            if (window.Game.ui) {
                window.Game.ui.addBattleLog(`第${battle.round}回合开始！`, 'info');
                window.Game.ui.updateBattleRound(battle.round);
            }
        }

        // 检查战斗是否结束
        if (this.checkBattleEnd()) {
            return;
        }

        // 继续下一个单位的回合
        if (this.isAutoBattle) {
            setTimeout(() => {
                this.processTurn();
            }, this.animationDelay);
        } else {
            this.processTurn();
        }
    }

    // 选择攻击目标
    selectTargets(unit) {
        const battle = this.currentBattle;
        let enemies;

        if (unit.side === 'player') {
            enemies = battle.turnOrder.filter(u => u.side === 'enemy' && u.health > 0);
        } else {
            enemies = battle.turnOrder.filter(u => u.side === 'player' && u.health > 0);
        }

        // 简化的AI：优先攻击血量最少的敌人
        return enemies.sort((a, b) => a.health - b.health);
    }

    // 计算伤害
    calculateDamage(attacker, target) {
        const attack = attacker.getAttack ? attacker.getAttack() : attacker.attack;
        const defense = target.getDefense ? target.getDefense() : target.defense;

        // 基础伤害计算
        let damage = Math.max(1, attack - defense);

        // 添加随机因子 (80%-120%)
        const randomFactor = 0.8 + Math.random() * 0.4;
        damage = Math.floor(damage * randomFactor);

        // 暴击判定 (10%概率)
        if (Math.random() < 0.1) {
            damage = Math.floor(damage * 1.5);
            if (window.Game.ui) {
                window.Game.ui.addBattleLog('暴击！', 'critical');
            }
        }

        return Math.max(1, damage);
    }

    // 检查战斗是否结束
    checkBattleEnd() {
        const battle = this.currentBattle;

        const alivePlayers = battle.turnOrder.filter(u => u.side === 'player' && u.health > 0);
        const aliveEnemies = battle.turnOrder.filter(u => u.side === 'enemy' && u.health > 0);

        if (alivePlayers.length === 0) {
            // 玩家失败
            setTimeout(() => {
                this.endBattle({ victory: false });
            }, this.animationDelay * 1.5);
            return true;
        } else if (aliveEnemies.length === 0) {
            // 玩家胜利
            setTimeout(() => {
                this.endBattle({ victory: true });
            }, this.animationDelay * 1.5);
            return true;
        } else if (battle.round > 20) {
            // 超过20回合，判定为平局（玩家失败）
            if (window.Game.ui) {
                window.Game.ui.addBattleLog('战斗时间过长，撤退！', 'info');
            }
            setTimeout(() => {
                this.endBattle({ victory: false });
            }, this.animationDelay * 1.5);
            return true;
        }

        return false;
    }

    // 计算战斗结果
    calculateBattleResult() {
        const playerPower = this.calculateTotalPower(this.currentBattle.playerUnits);
        const enemyPower = this.calculateTotalPower(this.currentBattle.enemyUnits);
        
        // 简单的战斗力对比
        const playerAdvantage = playerPower / (playerPower + enemyPower);
        const randomFactor = 0.8 + Math.random() * 0.4; // 0.8-1.2的随机因子
        
        const victory = (playerAdvantage * randomFactor) > 0.5;
        
        // 计算伤亡
        const casualties = this.calculateCasualties(victory);
        
        return {
            victory: victory,
            playerCasualties: casualties.player,
            enemyCasualties: casualties.enemy,
            playerPower: playerPower,
            enemyPower: enemyPower
        };
    }

    // 计算总战斗力
    calculateTotalPower(units) {
        let totalPower = 0;
        
        for (const unit of units) {
            if (unit.health > 0) {
                const attack = unit.getAttack ? unit.getAttack() : unit.attack;
                const defense = unit.getDefense ? unit.getDefense() : unit.defense;
                const healthRatio = unit.health / unit.maxHealth;
                
                totalPower += (attack + defense) * healthRatio;
            }
        }
        
        return totalPower;
    }

    // 计算伤亡
    calculateCasualties(victory) {
        const playerUnits = this.currentBattle.playerUnits;
        const enemyUnits = this.currentBattle.enemyUnits;
        
        let playerCasualties = 0;
        let enemyCasualties = 0;
        
        if (victory) {
            // 玩家胜利，敌军全灭，玩家有少量伤亡
            enemyCasualties = enemyUnits.length;
            playerCasualties = Math.floor(playerUnits.length * (0.1 + Math.random() * 0.2));
        } else {
            // 玩家失败，玩家损失较大
            playerCasualties = Math.floor(playerUnits.length * (0.3 + Math.random() * 0.4));
            enemyCasualties = Math.floor(enemyUnits.length * (0.2 + Math.random() * 0.3));
        }
        
        // 应用伤亡
        this.applyCasualties(playerUnits, playerCasualties);
        this.applyCasualties(enemyUnits, enemyCasualties);
        
        return {
            player: playerCasualties,
            enemy: enemyCasualties
        };
    }

    // 应用伤亡
    applyCasualties(units, casualties) {
        const shuffled = Utils.shuffle([...units]);
        
        for (let i = 0; i < Math.min(casualties, shuffled.length); i++) {
            shuffled[i].health = 0;
        }
        
        // 对剩余单位造成伤害
        for (let i = casualties; i < shuffled.length; i++) {
            const damageRatio = 0.1 + Math.random() * 0.3;
            shuffled[i].health = Math.max(1, Math.floor(shuffled[i].health * (1 - damageRatio)));
        }
    }

    // 结束战斗
    endBattle(result) {
        if (!this.currentBattle) return;

        const battle = this.currentBattle;
        
        if (result.victory) {
            // 玩家胜利
            this.handleVictory(battle);
        } else {
            // 玩家失败
            this.handleDefeat(battle);
        }
        
        // 更新军队状态
        if (window.Game.army) {
            window.Game.army.returnArmy(battle.playerUnits);
        }
        
        // 记录战斗历史
        this.battleHistory.push({
            stageId: battle.stageId,
            victory: result.victory,
            timestamp: Date.now(),
            playerCasualties: result.playerCasualties,
            enemyCasualties: result.enemyCasualties
        });
        
        GameEvents.emit(GAME_EVENTS.BATTLE_END, {
            battle: battle,
            result: result
        });

        // 延迟隐藏战斗界面
        setTimeout(() => {
            if (window.Game.ui) {
                window.Game.ui.hideBattleScreen();
            }
        }, 3000); // 3秒后自动关闭

        this.currentBattle = null;
    }

    // 处理胜利
    handleVictory(battle) {
        const rewards = battle.stageData.rewards;
        
        // 给予奖励
        if (rewards) {
            for (const [resource, amount] of Object.entries(rewards)) {
                if (resource === 'exp') {
                    if (window.Game.gainExp) {
                        window.Game.gainExp(amount);
                    }
                } else if (window.Game.resources) {
                    window.Game.resources.addResource(resource, amount);
                }
            }
        }
        
        GameEvents.emit(GAME_EVENTS.BATTLE_WIN, {
            stageId: battle.stageId,
            rewards: rewards
        });
        
        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.SUCCESS,
            text: `战斗胜利！获得奖励：${this.formatRewards(rewards)}`
        });
    }

    // 处理失败
    handleDefeat(battle) {
        GameEvents.emit(GAME_EVENTS.BATTLE_LOSE, {
            stageId: battle.stageId
        });

        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.ERROR,
            text: '战斗失败！请提升实力后再来挑战。'
        });
    }

    // 处理新手训练胜利
    handleTutorialVictory(stageData) {
        const rewards = stageData.rewards;

        // 给予奖励
        if (rewards) {
            for (const [resource, amount] of Object.entries(rewards)) {
                if (resource === 'exp') {
                    if (window.Game.gainExp) {
                        window.Game.gainExp(amount);
                    }
                } else if (window.Game.resources) {
                    window.Game.resources.addResource(resource, amount);
                }
            }
        }

        GameEvents.emit(GAME_EVENTS.BATTLE_WIN, {
            stageId: stageData.id,
            rewards: rewards
        });

        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.SUCCESS,
            text: `新手训练完成！获得奖励：${this.formatRewards(rewards)}`
        });
    }

    // 格式化奖励
    formatRewards(rewards) {
        if (!rewards) return '';
        
        const parts = [];
        for (const [resource, amount] of Object.entries(rewards)) {
            if (resource === 'exp') {
                parts.push(`${amount}经验`);
            } else {
                parts.push(`${amount}${this.getResourceName(resource)}`);
            }
        }
        return parts.join(', ');
    }

    // 获取资源名称
    getResourceName(resource) {
        const names = {
            gold: '金币',
            wood: '木材',
            stone: '石材',
            food: '食物'
        };
        return names[resource] || resource;
    }

    // 获取可用的战斗关卡
    getAvailableStages() {
        return GameData.battles.campaign.filter(stage => 
            window.Game.player.level >= stage.unlockLevel
        );
    }

    // 检查关卡是否解锁
    isStageUnlocked(stageId) {
        const stage = this.getBattleStage(stageId);
        return stage && window.Game.player.level >= stage.unlockLevel;
    }

    // 获取战斗历史
    getBattleHistory() {
        return [...this.battleHistory];
    }

    // 获取胜利次数
    getVictoryCount(stageId = null) {
        if (stageId) {
            return this.battleHistory.filter(battle => 
                battle.stageId === stageId && battle.victory
            ).length;
        } else {
            return this.battleHistory.filter(battle => battle.victory).length;
        }
    }

    // 重置战斗系统
    reset() {
        this.currentBattle = null;
        this.battleHistory = [];
    }

    // 加载战斗数据
    loadData(battleData) {
        if (battleData.battleHistory) {
            this.battleHistory = battleData.battleHistory;
        }
    }

    // 获取存档数据
    getSaveData() {
        return {
            currentBattle: this.currentBattle,
            battleHistory: this.battleHistory
        };
    }

    // 加载存档数据
    loadSaveData(data) {
        if (data.currentBattle) {
            this.currentBattle = data.currentBattle;
        }
        if (data.battleHistory) {
            this.battleHistory = data.battleHistory;
        }
    }

    // 设置战斗速度
    setSpeed(speed) {
        this.battleSpeed = speed;
        this.animationDelay = Math.max(200, 1000 / speed); // 最小200ms延迟
        console.log(`战斗速度设置为 ${speed}x，动画延迟 ${this.animationDelay}ms`);
    }

    // 设置自动战斗
    setAutoBattle(auto) {
        this.isAutoBattle = auto;
        if (auto && this.currentBattle) {
            this.startAutoBattle();
        }
    }

    // 开始自动战斗
    startAutoBattle() {
        if (!this.isAutoBattle || !this.currentBattle) return;
        
        const autoBattleLoop = () => {
            if (!this.isAutoBattle || !this.currentBattle) return;
            
            this.processTurn();
            
            if (this.currentBattle) {
                setTimeout(autoBattleLoop, this.animationDelay);
            }
        };
        
        setTimeout(autoBattleLoop, this.animationDelay);
    }

    // 跳过到战斗结果
    skipToResult() {
        if (!this.currentBattle) return;
        
        // 快速计算战斗结果
        const playerPower = this.calculateTotalPower(this.currentBattle.playerUnits);
        const enemyPower = this.calculateTotalPower(this.currentBattle.enemyUnits);
        
        // 添加一些随机性
        const playerLuck = Math.random() * 0.2 + 0.9; // 0.9-1.1倍
        const enemyLuck = Math.random() * 0.2 + 0.9;
        
        const finalPlayerPower = playerPower * playerLuck;
        const finalEnemyPower = enemyPower * enemyLuck;
        
        console.log(`快速战斗计算: 玩家${finalPlayerPower.toFixed(1)} vs 敌人${finalEnemyPower.toFixed(1)}`);
        
        if (finalPlayerPower > finalEnemyPower) {
            this.handleVictory();
        } else {
            this.handleDefeat();
        }
    }
}

// 导出战斗系统
window.BattleSystem = BattleSystem;
